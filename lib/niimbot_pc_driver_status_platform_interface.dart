import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'niimbot_pc_driver_status_method_channel.dart';

abstract class NiimbotPcDriverStatusPlatform extends PlatformInterface {
  /// Constructs a NiimbotPcDriverStatusPlatform.
  NiimbotPcDriverStatusPlatform() : super(token: _token);

  static final Object _token = Object();

  static NiimbotPcDriverStatusPlatform _instance = MethodChannelNiimbotPcDriverStatus();

  /// The default instance of [NiimbotPcDriverStatusPlatform] to use.
  ///
  /// Defaults to [MethodChannelNiimbotPcDriverStatus].
  static NiimbotPcDriverStatusPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [NiimbotPcDriverStatusPlatform] when
  /// they register themselves.
  static set instance(NiimbotPcDriverStatusPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
