import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'niimbot_pc_driver_status_platform_interface.dart';

/// An implementation of [NiimbotPcDriverStatusPlatform] that uses method channels.
class MethodChannelNiimbotPcDriverStatus extends NiimbotPcDriverStatusPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('niimbot_pc_driver_status');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
