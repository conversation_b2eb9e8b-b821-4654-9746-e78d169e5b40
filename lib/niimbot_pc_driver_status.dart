import 'dart:ffi';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:ffi/ffi.dart';
import 'niimbot_pc_driver_status_platform_interface.dart';

// 蓝牙错误码枚举
enum BluetoothErrorCode {
  success(0),                    // 成功
  noAdapter(1),                  // 没有蓝牙适配器
  driverNotInstalled(2),         // 驱动未安装
  driverDisabled(3),             // 驱动被禁用
  accessDenied(4),               // 访问被拒绝（需要管理员权限）
  serviceNotRunning(5),          // 蓝牙服务未运行
  systemError(6),                // 系统错误
  notSupported(7),               // 不支持的操作
  timeout(8),                    // 操作超时
  unknown(99);                   // 未知错误

  const BluetoothErrorCode(this.value);
  final int value;

  static BluetoothErrorCode fromValue(int value) {
    for (final code in BluetoothErrorCode.values) {
      if (code.value == value) return code;
    }
    return BluetoothErrorCode.unknown;
  }
}

// 蓝牙适配器状态类
class BluetoothAdapterStatus {
  final bool isPresent;              // 是否存在蓝牙硬件
  final bool isEnabled;              // 是否启用
  final bool isDriverWorking;        // 驱动是否正常工作
  final BluetoothErrorCode errorCode; // 错误码
  final String errorMessage;         // 错误信息描述
  final String adapterName;          // 适配器名称
  final String driverVersion;        // 驱动版本

  const BluetoothAdapterStatus({
    required this.isPresent,
    required this.isEnabled,
    required this.isDriverWorking,
    required this.errorCode,
    required this.errorMessage,
    required this.adapterName,
    required this.driverVersion,
  });

  @override
  String toString() {
    return 'BluetoothAdapterStatus('
        'isPresent: $isPresent, '
        'isEnabled: $isEnabled, '
        'isDriverWorking: $isDriverWorking, '
        'errorCode: $errorCode, '
        'errorMessage: "$errorMessage", '
        'adapterName: "$adapterName", '
        'driverVersion: "$driverVersion"'
        ')';
  }
}

// C结构体映射 - BluetoothAdapterStatus
final class BluetoothAdapterStatusNative extends Struct {
  @Uint8()
  external int isPresent;              // bool -> Uint8

  @Uint8()
  external int isEnabled;              // bool -> Uint8

  @Uint8()
  external int isDriverWorking;        // bool -> Uint8

  @Int32()
  external int errorCode;              // int

  @Array(256)
  external Array<Uint8> errorMessage; // char[256]

  @Array(128)
  external Array<Uint8> adapterName;  // char[128]

  @Array(64)
  external Array<Uint8> driverVersion; // char[64]
}

// Native function typedefs for stdcall (BT_CALL) functions
// Note: For stdcall functions on Windows, function names might be decorated
typedef IsBluetoothDriverWorkingNative = Uint8 Function();
typedef IsBluetoothDriverWorkingDart = int Function();

typedef IsBluetoothPresentNative = Uint8 Function();
typedef IsBluetoothPresentDart = int Function();

typedef IsBluetoothEnabledNative = Uint8 Function();
typedef IsBluetoothEnabledDart = int Function();

typedef GetLastErrorCodeNative = Int32 Function();
typedef GetLastErrorCodeDart = int Function();

typedef GetLastErrorMessageNative = Pointer<Utf8> Function();
typedef GetLastErrorMessageDart = Pointer<Utf8> Function();

typedef RefreshBluetoothStatusNative = Void Function();
typedef RefreshBluetoothStatusDart = void Function();

typedef ConfigureLoggerNative = Void Function(Int32 logLevel, Int32 logTarget, Pointer<Utf8> logPath);
typedef ConfigureLoggerDart = void Function(int logLevel, int logTarget, Pointer<Utf8> logPath);

typedef GetLibraryVersionNative = Pointer<Utf8> Function();
typedef GetLibraryVersionDart = Pointer<Utf8> Function();

typedef GetPlatformInfoNative = Pointer<Utf8> Function();
typedef GetPlatformInfoDart = Pointer<Utf8> Function();

// 新增：获取完整蓝牙适配器状态的函数
typedef GetBluetoothAdapterStatusNative = Void Function(Pointer<BluetoothAdapterStatusNative> status);
typedef GetBluetoothAdapterStatusDart = void Function(Pointer<BluetoothAdapterStatusNative> status);

class NiimbotPcDriverStatus {
  static DynamicLibrary? _dylib;
  
  // Function pointers
  static int Function()? _isBluetoothDriverWorking;
  static int Function()? _isBluetoothPresent;
  static int Function()? _isBluetoothEnabled;
  static GetLastErrorCodeDart? _getLastErrorCode;
  static GetLastErrorMessageDart? _getLastErrorMessage;
  static RefreshBluetoothStatusDart? _refreshBluetoothStatus;
  static ConfigureLoggerDart? _configureLogger;
  static GetLibraryVersionDart? _getLibraryVersion;
  static GetPlatformInfoDart? _getPlatformInfo;
  static GetBluetoothAdapterStatusDart? _getBluetoothAdapterStatus;

  // 辅助函数：将C字符数组转换为Dart字符串
  static String _arrayToString(Array<Uint8> array, int maxLength) {
    final List<int> bytes = [];
    for (int i = 0; i < maxLength; i++) {
      final byte = array[i];
      if (byte == 0) break; // 遇到null终止符停止
      bytes.add(byte);
    }
    return String.fromCharCodes(bytes);
  }

  static DynamicLibrary _loadLibrary() {
    if (_dylib != null) return _dylib!;

    final String libraryPath;
    if (Platform.isWindows) {
      final executableDir = path.dirname(Platform.resolvedExecutable);
      libraryPath = path.join(executableDir, 'bluetooth_detector.dll');

      if (!File(libraryPath).existsSync()) {
        throw Exception('bluetooth_detector.dll not found at: $libraryPath');
      }
    } else {
      throw UnsupportedError('This plugin only supports Windows');
    }

    _dylib = DynamicLibrary.open(libraryPath);
    return _dylib!;
  }

  // Helper function to try different function name variants for stdcall functions
  static Pointer<NativeFunction<T>> _lookupFunction<T extends Function>(
      DynamicLibrary dylib, String baseName) {
    // Try different name variants for stdcall functions
    final variants = [
      baseName,                    // Undecorated name (if .def file is used)
      '_$baseName@0',             // stdcall with 0 parameters
      '_$baseName@4',             // stdcall with 4 bytes parameters
      '_$baseName@8',             // stdcall with 8 bytes parameters
      '_$baseName@12',            // stdcall with 12 bytes parameters
      '_$baseName@16',            // stdcall with 16 bytes parameters
    ];

    for (final variant in variants) {
      try {
        final func = dylib.lookup<NativeFunction<T>>(variant);
        if (kDebugMode) {
          print('Found function $baseName using variant: $variant');
        }
        return func;
      } catch (e) {
        // Continue to next variant
      }
    }

    throw ArgumentError('Function $baseName not found with any variant');
  }

  // 新增：获取完整的蓝牙适配器状态（推荐使用）
  static BluetoothAdapterStatus getBluetoothAdapterStatus() {
    try {
      final dylib = _loadLibrary();

      _getBluetoothAdapterStatus ??= _lookupFunction<GetBluetoothAdapterStatusNative>(
          dylib, 'GetBluetoothAdapterStatus').asFunction();

      // 分配内存给结构体
      final statusPtr = calloc<BluetoothAdapterStatusNative>();
      try {
        // 调用C函数填充结构体
        _getBluetoothAdapterStatus!(statusPtr);

        final status = statusPtr.ref;

        // 转换为Dart对象
        return BluetoothAdapterStatus(
          isPresent: status.isPresent != 0,
          isEnabled: status.isEnabled != 0,
          isDriverWorking: status.isDriverWorking != 0,
          errorCode: BluetoothErrorCode.fromValue(status.errorCode),
          errorMessage: _arrayToString(status.errorMessage, 256),
          adapterName: _arrayToString(status.adapterName, 128),
          driverVersion: _arrayToString(status.driverVersion, 64),
        );
      } finally {
        calloc.free(statusPtr);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getBluetoothAdapterStatus: $e');
      }
      // 返回默认状态
      return const BluetoothAdapterStatus(
        isPresent: false,
        isEnabled: false,
        isDriverWorking: false,
        errorCode: BluetoothErrorCode.unknown,
        errorMessage: 'Failed to get status',
        adapterName: '',
        driverVersion: '',
      );
    }
  }

  // 保持向后兼容的单独函数
  static bool isBluetoothDriverWorking() {
    try {
      final dylib = _loadLibrary();

      _isBluetoothDriverWorking ??= _lookupFunction<IsBluetoothDriverWorkingNative>(
          dylib, 'IsBluetoothDriverWorking').asFunction();

      return _isBluetoothDriverWorking!() != 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error calling isBluetoothDriverWorking: $e');
      }
      return false;
    }
  }

  static bool isBluetoothPresent() {
    try {
      final dylib = _loadLibrary();

      _isBluetoothPresent ??= _lookupFunction<IsBluetoothPresentNative>(
          dylib, 'IsBluetoothPresent').asFunction();

      return _isBluetoothPresent!() != 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error calling isBluetoothPresent: $e');
      }
      return false;
    }
  }

  static bool isBluetoothEnabled() {
    try {
      final dylib = _loadLibrary();

      _isBluetoothEnabled ??= _lookupFunction<IsBluetoothEnabledNative>(
          dylib, 'IsBluetoothEnabled').asFunction();

      return _isBluetoothEnabled!() != 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error calling isBluetoothEnabled: $e');
      }
      return false;
    }
  }

  static int getLastErrorCode() {
    try {
      final dylib = _loadLibrary();

      _getLastErrorCode ??= _lookupFunction<GetLastErrorCodeNative>(
          dylib, 'GetLastErrorCode').asFunction();

      return _getLastErrorCode!();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLastErrorCode: $e');
      }
      return -1;
    }
  }

  static String getLastErrorMessage() {
    try {
      final dylib = _loadLibrary();

      _getLastErrorMessage ??= _lookupFunction<GetLastErrorMessageNative>(
          dylib, 'GetLastErrorMessage').asFunction();

      final ptr = _getLastErrorMessage!();
      if (ptr == nullptr) {
        return '';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLastErrorMessage: $e');
      }
      return 'Error retrieving message';
    }
  }

  static void refreshBluetoothStatus() {
    try {
      final dylib = _loadLibrary();

      _refreshBluetoothStatus ??= _lookupFunction<RefreshBluetoothStatusNative>(
          dylib, 'RefreshBluetoothStatus').asFunction();

      _refreshBluetoothStatus!();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling refreshBluetoothStatus: $e');
      }
    }
  }

  static void configureLogger(int logLevel, int logTarget, String logPath) {
    try {
      final dylib = _loadLibrary();

      _configureLogger ??= _lookupFunction<ConfigureLoggerNative>(
          dylib, 'ConfigureLogger').asFunction();

      final logPathPtr = logPath.toNativeUtf8();
      try {
        _configureLogger!(logLevel, logTarget, logPathPtr);
      } finally {
        malloc.free(logPathPtr);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error calling configureLogger: $e');
      }
    }
  }

  static String getLibraryVersion() {
    try {
      final dylib = _loadLibrary();

      _getLibraryVersion ??= _lookupFunction<GetLibraryVersionNative>(
          dylib, 'GetLibraryVersion').asFunction();

      final ptr = _getLibraryVersion!();
      if (ptr == nullptr) {
        return 'Unknown';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLibraryVersion: $e');
      }
      return 'Error';
    }
  }

  static String getPlatformInfo() {
    try {
      final dylib = _loadLibrary();

      _getPlatformInfo ??= _lookupFunction<GetPlatformInfoNative>(
          dylib, 'getPlatformInfo').asFunction();

      final ptr = _getPlatformInfo!();
      if (ptr == nullptr) {
        return 'Unknown';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getPlatformInfo: $e');
      }
      return 'Error';
    }
  }

  static String getLibraryVersion() {
    try {
      final dylib = _loadLibrary();
      
      _getLibraryVersion ??= dylib
          .lookup<NativeFunction<GetLibraryVersionNative>>(
              'GetLibraryVersion')
          .asFunction();

      final ptr = _getLibraryVersion!();
      if (ptr == nullptr) {
        return 'Unknown';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLibraryVersion: $e');
      }
      return 'Error';
    }
  }

  static String getPlatformInfo() {
    try {
      final dylib = _loadLibrary();
      
      _getPlatformInfo ??= dylib
          .lookup<NativeFunction<GetPlatformInfoNative>>(
              'GetPlatformInfo')
          .asFunction();

      final ptr = _getPlatformInfo!();
      if (ptr == nullptr) {
        return 'Unknown';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getPlatformInfo: $e');
      }
      return 'Error';
    }
  }

  Future<String?> getPlatformVersion() {
    return NiimbotPcDriverStatusPlatform.instance.getPlatformVersion();
  }
}
