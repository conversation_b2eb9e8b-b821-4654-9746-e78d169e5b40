import 'dart:ffi';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:ffi/ffi.dart';
import 'niimbot_pc_driver_status_platform_interface.dart';

// Native function typedefs
typedef IsBluetoothDriverWorkingNative = Uint8 Function();
typedef IsBluetoothDriverWorkingDart = int Function();

typedef IsBluetoothPresentNative = Uint8 Function();
typedef IsBluetoothPresentDart = int Function();

typedef IsBluetoothEnabledNative = Uint8 Function();
typedef IsBluetoothEnabledDart = int Function();

typedef GetLastErrorCodeNative = Int32 Function();
typedef GetLastErrorCodeDart = int Function();

typedef GetLastErrorMessageNative = Pointer<Utf8> Function();
typedef GetLastErrorMessageDart = Pointer<Utf8> Function();

typedef RefreshBluetoothStatusNative = Void Function();
typedef RefreshBluetoothStatusDart = void Function();

typedef ConfigureLoggerNative = Void Function(Int32 logLevel, Int32 logTarget, Pointer<Utf8> logPath);
typedef ConfigureLoggerDart = void Function(int logLevel, int logTarget, Pointer<Utf8> logPath);

typedef GetLibraryVersionNative = Pointer<Utf8> Function();
typedef GetLibraryVersionDart = Pointer<Utf8> Function();

typedef GetPlatformInfoNative = Pointer<Utf8> Function();
typedef GetPlatformInfoDart = Pointer<Utf8> Function();

class NiimbotPcDriverStatus {
  static DynamicLibrary? _dylib;
  
  // Function pointers
  static int Function()? _isBluetoothDriverWorking;
  static int Function()? _isBluetoothPresent;
  static int Function()? _isBluetoothEnabled;
  static GetLastErrorCodeDart? _getLastErrorCode;
  static GetLastErrorMessageDart? _getLastErrorMessage;
  static RefreshBluetoothStatusDart? _refreshBluetoothStatus;
  static ConfigureLoggerDart? _configureLogger;
  static GetLibraryVersionDart? _getLibraryVersion;
  static GetPlatformInfoDart? _getPlatformInfo;

  static DynamicLibrary _loadLibrary() {
    if (_dylib != null) return _dylib!;

    final String libraryPath;
    if (Platform.isWindows) {
      final executableDir = path.dirname(Platform.resolvedExecutable);
      libraryPath = path.join(executableDir, 'bluetooth_detector.dll');
      
      if (!File(libraryPath).existsSync()) {
        throw Exception('bluetooth_detector.dll not found at: $libraryPath');
      }
    } else {
      throw UnsupportedError('This plugin only supports Windows');
    }

    _dylib = DynamicLibrary.open(libraryPath);
    return _dylib!;
  }

  static bool isBluetoothDriverWorking() {
    try {
      final dylib = _loadLibrary();
      
      _isBluetoothDriverWorking ??= dylib
          .lookup<NativeFunction<IsBluetoothDriverWorkingNative>>(
              'IsBluetoothDriverWorking')
          .asFunction();

      return _isBluetoothDriverWorking!() != 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error calling isBluetoothDriverWorking: $e');
      }
      return false;
    }
  }

  static bool isBluetoothPresent() {
    try {
      final dylib = _loadLibrary();
      
      _isBluetoothPresent ??= dylib
          .lookup<NativeFunction<IsBluetoothPresentNative>>(
              'IsBluetoothPresent')
          .asFunction();

      return _isBluetoothPresent!() != 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error calling isBluetoothPresent: $e');
      }
      return false;
    }
  }

  static bool isBluetoothEnabled() {
    try {
      final dylib = _loadLibrary();
      
      _isBluetoothEnabled ??= dylib
          .lookup<NativeFunction<IsBluetoothEnabledNative>>(
              'IsBluetoothEnabled')
          .asFunction();

      return _isBluetoothEnabled!() != 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error calling isBluetoothEnabled: $e');
      }
      return false;
    }
  }

  static int getLastErrorCode() {
    try {
      final dylib = _loadLibrary();
      
      _getLastErrorCode ??= dylib
          .lookup<NativeFunction<GetLastErrorCodeNative>>(
              'GetLastErrorCode')
          .asFunction();

      return _getLastErrorCode!();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLastErrorCode: $e');
      }
      return -1;
    }
  }

  static String getLastErrorMessage() {
    try {
      final dylib = _loadLibrary();
      
      _getLastErrorMessage ??= dylib
          .lookup<NativeFunction<GetLastErrorMessageNative>>(
              'GetLastErrorMessage')
          .asFunction();

      final ptr = _getLastErrorMessage!();
      if (ptr == nullptr) {
        return '';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLastErrorMessage: $e');
      }
      return 'Error retrieving message';
    }
  }

  static void refreshBluetoothStatus() {
    try {
      final dylib = _loadLibrary();
      
      _refreshBluetoothStatus ??= dylib
          .lookup<NativeFunction<RefreshBluetoothStatusNative>>(
              'RefreshBluetoothStatus')
          .asFunction();

      _refreshBluetoothStatus!();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling refreshBluetoothStatus: $e');
      }
    }
  }

  static void configureLogger(int logLevel, int logTarget, String logPath) {
    try {
      final dylib = _loadLibrary();
      
      _configureLogger ??= dylib
          .lookup<NativeFunction<ConfigureLoggerNative>>(
              'ConfigureLogger')
          .asFunction();

      final logPathPtr = logPath.toNativeUtf8();
      try {
        _configureLogger!(logLevel, logTarget, logPathPtr);
      } finally {
        malloc.free(logPathPtr);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error calling configureLogger: $e');
      }
    }
  }

  static String getLibraryVersion() {
    try {
      final dylib = _loadLibrary();
      
      _getLibraryVersion ??= dylib
          .lookup<NativeFunction<GetLibraryVersionNative>>(
              'GetLibraryVersion')
          .asFunction();

      final ptr = _getLibraryVersion!();
      if (ptr == nullptr) {
        return 'Unknown';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLibraryVersion: $e');
      }
      return 'Error';
    }
  }

  static String getPlatformInfo() {
    try {
      final dylib = _loadLibrary();
      
      _getPlatformInfo ??= dylib
          .lookup<NativeFunction<GetPlatformInfoNative>>(
              'GetPlatformInfo')
          .asFunction();

      final ptr = _getPlatformInfo!();
      if (ptr == nullptr) {
        return 'Unknown';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getPlatformInfo: $e');
      }
      return 'Error';
    }
  }

  Future<String?> getPlatformVersion() {
    return NiimbotPcDriverStatusPlatform.instance.getPlatformVersion();
  }
}
