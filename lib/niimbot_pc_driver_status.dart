import 'dart:ffi';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:ffi/ffi.dart';
import 'niimbot_pc_driver_status_platform_interface.dart';

// Native function typedefs for stdcall (BT_CALL) functions
// Note: For stdcall functions on Windows, function names might be decorated
typedef IsBluetoothDriverWorkingNative = Uint8 Function();
typedef IsBluetoothDriverWorkingDart = int Function();

typedef IsBluetoothPresentNative = Uint8 Function();
typedef IsBluetoothPresentDart = int Function();

typedef IsBluetoothEnabledNative = Uint8 Function();
typedef IsBluetoothEnabledDart = int Function();

typedef GetLastErrorCodeNative = Int32 Function();
typedef GetLastErrorCodeDart = int Function();

typedef GetLastErrorMessageNative = Pointer<Utf8> Function();
typedef GetLastErrorMessageDart = Pointer<Utf8> Function();

typedef RefreshBluetoothStatusNative = Void Function();
typedef RefreshBluetoothStatusDart = void Function();

typedef ConfigureLoggerNative = Void Function(Int32 logLevel, Int32 logTarget, Pointer<Utf8> logPath);
typedef ConfigureLoggerDart = void Function(int logLevel, int logTarget, Pointer<Utf8> logPath);

typedef GetLibraryVersionNative = Pointer<Utf8> Function();
typedef GetLibraryVersionDart = Pointer<Utf8> Function();

typedef GetPlatformInfoNative = Pointer<Utf8> Function();
typedef GetPlatformInfoDart = Pointer<Utf8> Function();

class NiimbotPcDriverStatus {
  static DynamicLibrary? _dylib;
  
  // Function pointers
  static int Function()? _isBluetoothDriverWorking;
  static int Function()? _isBluetoothPresent;
  static int Function()? _isBluetoothEnabled;
  static GetLastErrorCodeDart? _getLastErrorCode;
  static GetLastErrorMessageDart? _getLastErrorMessage;
  static RefreshBluetoothStatusDart? _refreshBluetoothStatus;
  static ConfigureLoggerDart? _configureLogger;
  static GetLibraryVersionDart? _getLibraryVersion;
  static GetPlatformInfoDart? _getPlatformInfo;

  static DynamicLibrary _loadLibrary() {
    if (_dylib != null) return _dylib!;

    final String libraryPath;
    if (Platform.isWindows) {
      final executableDir = path.dirname(Platform.resolvedExecutable);
      libraryPath = path.join(executableDir, 'bluetooth_detector.dll');

      if (!File(libraryPath).existsSync()) {
        throw Exception('bluetooth_detector.dll not found at: $libraryPath');
      }
    } else {
      throw UnsupportedError('This plugin only supports Windows');
    }

    _dylib = DynamicLibrary.open(libraryPath);
    return _dylib!;
  }

  // Helper function to try different function name variants for stdcall functions
  static Pointer<NativeFunction<T>> _lookupFunction<T extends Function>(
      DynamicLibrary dylib, String baseName) {
    // Try different name variants for stdcall functions
    final variants = [
      baseName,                    // Undecorated name (if .def file is used)
      '_$baseName@0',             // stdcall with 0 parameters
      '_$baseName@4',             // stdcall with 4 bytes parameters
      '_$baseName@8',             // stdcall with 8 bytes parameters
      '_$baseName@12',            // stdcall with 12 bytes parameters
      '_$baseName@16',            // stdcall with 16 bytes parameters
    ];

    for (final variant in variants) {
      try {
        final func = dylib.lookup<NativeFunction<T>>(variant);
        if (kDebugMode) {
          print('Found function $baseName using variant: $variant');
        }
        return func;
      } catch (e) {
        // Continue to next variant
      }
    }

    throw ArgumentError('Function $baseName not found with any variant');
  }

  static bool isBluetoothDriverWorking() {
    try {
      final dylib = _loadLibrary();

      _isBluetoothDriverWorking ??= _lookupFunction<IsBluetoothDriverWorkingNative>(
          dylib, 'IsBluetoothDriverWorking').asFunction();

      return _isBluetoothDriverWorking!() != 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error calling isBluetoothDriverWorking: $e');
      }
      return false;
    }
  }

  static bool isBluetoothPresent() {
    try {
      final dylib = _loadLibrary();

      _isBluetoothPresent ??= _lookupFunction<IsBluetoothPresentNative>(
          dylib, 'IsBluetoothPresent').asFunction();

      return _isBluetoothPresent!() != 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error calling isBluetoothPresent: $e');
      }
      return false;
    }
  }

  static bool isBluetoothEnabled() {
    try {
      final dylib = _loadLibrary();

      _isBluetoothEnabled ??= _lookupFunction<IsBluetoothEnabledNative>(
          dylib, 'IsBluetoothEnabled').asFunction();

      return _isBluetoothEnabled!() != 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error calling isBluetoothEnabled: $e');
      }
      return false;
    }
  }

  static int getLastErrorCode() {
    try {
      final dylib = _loadLibrary();

      _getLastErrorCode ??= _lookupFunction<GetLastErrorCodeNative>(
          dylib, 'GetLastErrorCode').asFunction();

      return _getLastErrorCode!();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLastErrorCode: $e');
      }
      return -1;
    }
  }

  static String getLastErrorMessage() {
    try {
      final dylib = _loadLibrary();

      _getLastErrorMessage ??= _lookupFunction<GetLastErrorMessageNative>(
          dylib, 'GetLastErrorMessage').asFunction();

      final ptr = _getLastErrorMessage!();
      if (ptr == nullptr) {
        return '';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLastErrorMessage: $e');
      }
      return 'Error retrieving message';
    }
  }

  static void refreshBluetoothStatus() {
    try {
      final dylib = _loadLibrary();

      _refreshBluetoothStatus ??= _lookupFunction<RefreshBluetoothStatusNative>(
          dylib, 'RefreshBluetoothStatus').asFunction();

      _refreshBluetoothStatus!();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling refreshBluetoothStatus: $e');
      }
    }
  }

  static void configureLogger(int logLevel, int logTarget, String logPath) {
    try {
      final dylib = _loadLibrary();
      
      _configureLogger ??= dylib
          .lookup<NativeFunction<ConfigureLoggerNative>>(
              'ConfigureLogger')
          .asFunction();

      final logPathPtr = logPath.toNativeUtf8();
      try {
        _configureLogger!(logLevel, logTarget, logPathPtr);
      } finally {
        malloc.free(logPathPtr);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error calling configureLogger: $e');
      }
    }
  }

  static String getLibraryVersion() {
    try {
      final dylib = _loadLibrary();
      
      _getLibraryVersion ??= dylib
          .lookup<NativeFunction<GetLibraryVersionNative>>(
              'GetLibraryVersion')
          .asFunction();

      final ptr = _getLibraryVersion!();
      if (ptr == nullptr) {
        return 'Unknown';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getLibraryVersion: $e');
      }
      return 'Error';
    }
  }

  static String getPlatformInfo() {
    try {
      final dylib = _loadLibrary();
      
      _getPlatformInfo ??= dylib
          .lookup<NativeFunction<GetPlatformInfoNative>>(
              'GetPlatformInfo')
          .asFunction();

      final ptr = _getPlatformInfo!();
      if (ptr == nullptr) {
        return 'Unknown';
      }
      return ptr.toDartString();
    } catch (e) {
      if (kDebugMode) {
        print('Error calling getPlatformInfo: $e');
      }
      return 'Error';
    }
  }

  Future<String?> getPlatformVersion() {
    return NiimbotPcDriverStatusPlatform.instance.getPlatformVersion();
  }
}
