import 'package:flutter/material.dart';
import 'package:niimbot_pc_driver_status/niimbot_pc_driver_status.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'driver_status',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const BluetoothStatusPage(),
    );
  }
}

class BluetoothStatusPage extends StatefulWidget {
  const BluetoothStatusPage({super.key});

  @override
  State<BluetoothStatusPage> createState() => BluetoothStatusPageState();
}

class BluetoothStatusPageState extends State<BluetoothStatusPage> {
  String _bluetoothStatus = "正在检测...";
  bool _isLoading = true;
  bool? _driverStatus;
  bool? _bluetoothPresent;
  bool? _bluetoothEnabled;
  String? _errorMessage;
  String? _libraryVersion;
  String? _platformInfo;

  @override
  void initState() {
    super.initState();
    _checkBluetoothAndDriverStatus();
  }

  Future<void> _checkBluetoothAndDriverStatus() async {
    setState(() {
      _isLoading = true;
      _bluetoothStatus = "正在检测蓝牙驱动...";
      _errorMessage = null;
    });

    try {
      // 刷新蓝牙状态
      NiimbotPcDriverStatus.refreshBluetoothStatus();
      
      // 获取库版本和平台信息
      _libraryVersion = NiimbotPcDriverStatus.getLibraryVersion();
      _platformInfo = NiimbotPcDriverStatus.getPlatformInfo();
      
      // 检测蓝牙驱动状态 (通过 FFI 调用原生 DLL)
      bool isDriverOk = NiimbotPcDriverStatus.isBluetoothDriverWorking();
      bool isPresent = NiimbotPcDriverStatus.isBluetoothPresent();
      bool isEnabled = NiimbotPcDriverStatus.isBluetoothEnabled();
      
      // 如果有错误，获取错误信息
      if (!isDriverOk || !isPresent || !isEnabled) {
        int errorCode = NiimbotPcDriverStatus.getLastErrorCode();
        String errorMsg = NiimbotPcDriverStatus.getLastErrorMessage();
        if (errorCode != 0) {
          _errorMessage = "错误代码: $errorCode\n错误信息: $errorMsg";
        }
      }

      setState(() {
        _driverStatus = isDriverOk;
        _bluetoothPresent = isPresent;
        _bluetoothEnabled = isEnabled;
        _isLoading = false;

        if (isDriverOk && isPresent && isEnabled) {
          _bluetoothStatus = "蓝牙驱动正常，硬件支持蓝牙功能，蓝牙已启用。";
        } else if (isDriverOk && isPresent && !isEnabled) {
          _bluetoothStatus = "蓝牙驱动正常，硬件支持蓝牙功能，但蓝牙未启用。";
        } else if (isDriverOk && !isPresent) {
          _bluetoothStatus = "蓝牙驱动正常，但未检测到蓝牙硬件。";
        } else {
          _bluetoothStatus = "蓝牙驱动异常或未安装，请检查设备管理器。";
        }
      });
    } catch (e) {
      setState(() {
        _bluetoothStatus = "检测过程中发生错误: $e";
        _isLoading = false;
        _driverStatus = false;
        _bluetoothPresent = false;
        _bluetoothEnabled = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("蓝牙状态检测"),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getBluetoothIcon(),
                size: 80,
                color: _getBluetoothIconColor(),
              ),
              const SizedBox(height: 20),
              Text(
                '蓝牙状态',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  children: [
                    if (_isLoading) ...[
                      const CircularProgressIndicator(),
                      const SizedBox(height: 10),
                    ],
                    Text(
                      _bluetoothStatus,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
              ElevatedButton.icon(
                onPressed: _isLoading ? null : _checkBluetoothAndDriverStatus,
                icon: const Icon(Icons.refresh),
                label: const Text('重新检测'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
              ),
              const SizedBox(height: 20),
              if (_libraryVersion != null || _platformInfo != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_libraryVersion != null)
                        Text(
                          '库版本: $_libraryVersion',
                          style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                        ),
                      if (_platformInfo != null)
                        Text(
                          '平台信息: $_platformInfo',
                          style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                        ),
                    ],
                  ),
                ),
              ],
              if (_driverStatus != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _driverStatus! ? Colors.green[50] : Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _driverStatus!
                          ? Colors.green[200]!
                          : Colors.red[200]!,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        '驱动状态',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: _driverStatus!
                                      ? Colors.green[700]
                                      : Colors.red[700],
                                ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: _driverStatus!
                              ? Colors.green[100]
                              : Colors.red[100],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _driverStatus! ? Colors.green : Colors.red,
                          ),
                        ),
                        child: Text(
                          _driverStatus! ? '正常' : '异常',
                          style: TextStyle(
                            fontSize: 14,
                            color: _driverStatus!
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              if (_bluetoothPresent != null && _bluetoothEnabled != null) ...[                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildStatusChip(
                      '蓝牙硬件',
                      _bluetoothPresent!,
                      _bluetoothPresent! ? Icons.check_circle : Icons.cancel,
                    ),
                    const SizedBox(width: 10),
                    _buildStatusChip(
                      '蓝牙启用',
                      _bluetoothEnabled!,
                      _bluetoothEnabled! ? Icons.bluetooth : Icons.bluetooth_disabled,
                    ),
                  ],
                ),
              ],
              if (_errorMessage != null) ...[                const SizedBox(height: 10),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange[200]!),
                  ),
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(fontSize: 12, color: Colors.orange[700]),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  IconData _getBluetoothIcon() {
    if (_isLoading) {
      return Icons.bluetooth_searching;
    }

    if (_driverStatus == null) {
      return Icons.bluetooth_searching;
    }

    if (_driverStatus == false) {
      return Icons.bluetooth_disabled;
    }

    return Icons.bluetooth;
  }

  Widget _buildStatusChip(String label, bool status, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: status ? Colors.green[50] : Colors.grey[100],
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: status ? Colors.green[300]! : Colors.grey[300]!,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: status ? Colors.green[700] : Colors.grey[600],
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: status ? Colors.green[700] : Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getBluetoothIconColor() {
    if (_isLoading || _driverStatus == null) {
      return Colors.grey;
    }

    if (_driverStatus == false) {
      return Colors.red;
    }

    return Colors.blue;
  }
}
