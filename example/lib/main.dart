import 'package:flutter/material.dart';
import 'package:niimbot_pc_driver_status/bluetooth_driver_detector.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'driver_status',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const BluetoothStatusPage(),
    );
  }
}

class BluetoothStatusPage extends StatefulWidget {
  const BluetoothStatusPage({super.key});

  @override
  _BluetoothStatusPageState createState() => _BluetoothStatusPageState();
}

class _BluetoothStatusPageState extends State<BluetoothStatusPage> {
  String _bluetoothStatus = "正在检测...";
  bool _isLoading = true;
  bool? _driverStatus;

  @override
  void initState() {
    super.initState();
    _checkBluetoothAndDriverStatus();
  }

  Future<void> _checkBluetoothAndDriverStatus() async {
    setState(() {
      _isLoading = true;
      _bluetoothStatus = "正在检测蓝牙驱动...";
    });

    try {
      // 检测蓝牙驱动状态 (通过 FFI 调用原生 DLL)
      bool isDriverOk = BluetoothDriverDetector.isBluetoothDriverWorking();

      setState(() {
        _driverStatus = isDriverOk;
        _isLoading = false;

        if (isDriverOk) {
          _bluetoothStatus = "蓝牙驱动正常，硬件支持蓝牙功能。";
        } else {
          _bluetoothStatus = "蓝牙驱动异常或未安装，请检查设备管理器。";
        }
      });
    } catch (e) {
      setState(() {
        _bluetoothStatus = "检测过程中发生错误: $e";
        _isLoading = false;
        _driverStatus = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("蓝牙状态检测"),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getBluetoothIcon(),
                size: 80,
                color: _getBluetoothIconColor(),
              ),
              const SizedBox(height: 20),
              Text(
                '蓝牙状态',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  children: [
                    if (_isLoading) ...[
                      const CircularProgressIndicator(),
                      const SizedBox(height: 10),
                    ],
                    Text(
                      _bluetoothStatus,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
              ElevatedButton.icon(
                onPressed: _isLoading ? null : _checkBluetoothAndDriverStatus,
                icon: const Icon(Icons.refresh),
                label: const Text('重新检测'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
              ),
              const SizedBox(height: 20),
              if (_driverStatus != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _driverStatus! ? Colors.green[50] : Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _driverStatus!
                          ? Colors.green[200]!
                          : Colors.red[200]!,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        '驱动状态',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: _driverStatus!
                                      ? Colors.green[700]
                                      : Colors.red[700],
                                ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: _driverStatus!
                              ? Colors.green[100]
                              : Colors.red[100],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _driverStatus! ? Colors.green : Colors.red,
                          ),
                        ),
                        child: Text(
                          _driverStatus! ? '正常' : '异常',
                          style: TextStyle(
                            fontSize: 14,
                            color: _driverStatus!
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  IconData _getBluetoothIcon() {
    if (_isLoading) {
      return Icons.bluetooth_searching;
    }

    if (_driverStatus == null) {
      return Icons.bluetooth_searching;
    }

    if (_driverStatus == false) {
      return Icons.bluetooth_disabled;
    }

    return Icons.bluetooth;
  }

  Color _getBluetoothIconColor() {
    if (_isLoading || _driverStatus == null) {
      return Colors.grey;
    }

    if (_driverStatus == false) {
      return Colors.red;
    }

    return Colors.blue;
  }
}
