import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_pc_driver_status/niimbot_pc_driver_status.dart';
import 'package:niimbot_pc_driver_status/niimbot_pc_driver_status_platform_interface.dart';
import 'package:niimbot_pc_driver_status/niimbot_pc_driver_status_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockNiimbotPcDriverStatusPlatform
    with MockPlatformInterfaceMixin
    implements NiimbotPcDriverStatusPlatform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final NiimbotPcDriverStatusPlatform initialPlatform = NiimbotPcDriverStatusPlatform.instance;

  test('$MethodChannelNiimbotPcDriverStatus is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelNiimbotPcDriverStatus>());
  });

  test('getPlatformVersion', () async {
    NiimbotPcDriverStatus niimbotPcDriverStatusPlugin = NiimbotPcDriverStatus();
    MockNiimbotPcDriverStatusPlatform fakePlatform = MockNiimbotPcDriverStatusPlatform();
    NiimbotPcDriverStatusPlatform.instance = fakePlatform;

    expect(await niimbotPcDriverStatusPlugin.getPlatformVersion(), '42');
  });
}
