# DLL Loading Guide for niimbot_pc_driver_status

## Overview
This Flutter plugin uses FFI (Foreign Function Interface) to load a native Windows DLL (`bluetooth_detector.dll`). The plugin implements a robust DLL loading mechanism that works in various scenarios.

## DLL Search Order
The plugin searches for `bluetooth_detector.dll` in the following order:

1. **Production (Built .exe)**: Next to the executable
   - Path: `<exe_directory>/bluetooth_detector.dll`

2. **Development (Example app)**: In the build output directory
   - `build/windows/x64/runner/Debug/bluetooth_detector.dll`
   - `build/windows/x64/runner/Release/bluetooth_detector.dll`
   - `build/windows/runner/Debug/bluetooth_detector.dll`
   - `build/windows/runner/Release/bluetooth_detector.dll`

3. **Plugin Development**: In the plugin's windows/libs folder
   - Path: `<plugin_root>/windows/libs/bluetooth_detector.dll`

4. **System PATH**: As a fallback, tries to load from system PATH

## How It Works

### Path Resolution
The plugin uses `Platform.resolvedExecutable` to find the executable's directory for production scenarios. For development, it checks common build output directories.

### Plugin Root Detection
The plugin automatically detects its root directory by searching for a `pubspec.yaml` file containing `name: niimbot_pc_driver_status`.

### Error Handling
If the DLL cannot be loaded, the plugin provides detailed debug output showing all paths that were searched, helping developers troubleshoot issues.

## Configuration

### CMakeLists.txt
The `windows/CMakeLists.txt` file is configured to bundle the DLL with the plugin:

```cmake
set(niimbot_pc_driver_status_bundled_libraries
  "${CMAKE_CURRENT_SOURCE_DIR}/libs/bluetooth_detector.dll"
  PARENT_SCOPE
)
```

### pubspec.yaml
The plugin is configured as an FFI plugin:

```yaml
flutter:
  plugin:
    platforms:
      windows:
        pluginClass: NiimbotPcDriverStatusPluginCApi
        ffiPlugin: true
```

## Troubleshooting

### DLL Not Found
If you see "Failed to load bluetooth_detector.dll", check:
1. The DLL exists in one of the search paths
2. The DLL architecture (x86/x64) matches your application
3. All DLL dependencies are available

### Debug Output
Run your app in debug mode to see detailed path search information:
```
Loading bluetooth_detector.dll from: C:\path\to\dll
```

### Common Issues
- **Error 126**: DLL or its dependencies not found
- **Error 193**: Architecture mismatch (32-bit vs 64-bit)

## Building for Release
When building for release, the DLL will be automatically bundled with your application thanks to the CMakeLists.txt configuration.