cmake_minimum_required(VERSION 3.14)

set(PROJECT_NAME "niimbot_pc_driver_status")
project(${PROJECT_NAME} LANGUAGES CXX)

# === 构建插件主共享库 ===
add_library(${PROJECT_NAME}_plugin SHARED
        "niimbot_pc_driver_status_plugin.cpp"  # 插件主入口
)

# === 使用 Flutter 的标准构建配置 ===
apply_standard_settings(${PROJECT_NAME}_plugin)

# === 插件接口头文件路径 ===
target_include_directories(${PROJECT_NAME}_plugin PUBLIC
        "${CMAKE_CURRENT_SOURCE_DIR}/include"
)

# === 链接 Flutter embedding 和 Wrapper ===
target_link_libraries(${PROJECT_NAME}_plugin
        flutter
        flutter_wrapper_plugin
)

# === 解决 C4819 编码错误：强制 MSVC 编译器以 UTF-8 编码处理源文件 ===
if (MSVC)
    target_compile_options(${PROJECT_NAME}_plugin PRIVATE /utf-8)
endif()

# === DLL 自动打包策略 ===
# 定义 DLL 的源目录，假设您的 DLL 位于插件项目根目录下的 windows/libs/ 目录中
# 并按架构分类 (e.g., windows/libs/x64/bluetooth_detector.dll)
set(BLUETOOTH_DLL_ROOT_DIR "${CMAKE_CURRENT_SOURCE_DIR}/libs")

# 根据目标架构选择正确的 DLL 路径
#if (CMAKE_SIZEOF_VOID_P EQUAL 8) # x64 架构
#    set(BLUETOOTH_DLL_PATH "${BLUETOOTH_DLL_ROOT_DIR}/x64/bluetooth_detector.dll")
#elseif (CMAKE_SYSTEM_PROCESSOR MATCHES "^arm64$") # ARM64 架构
#    set(BLUETOOTH_DLL_PATH "${BLUETOOTH_DLL_ROOT_DIR}/arm64/bluetooth_detector.dll")
#else() # 默认 x86 架构
#    set(BLUETOOTH_DLL_PATH "${BLUETOOTH_DLL_ROOT_DIR}/x86/bluetooth_detector.dll")
#endif()
set(BLUETOOTH_DLL_PATH "${BLUETOOTH_DLL_ROOT_DIR}/bluetooth_detector.dll")

# 检查 DLL 是否存在，如果不存在则发出警告
if (NOT EXISTS "${BLUETOOTH_DLL_PATH}")
    message(WARNING "bluetooth_detector.dll not found for current architecture at: ${BLUETOOTH_DLL_PATH}")
endif()

# 将 DLL 复制到插件的构建输出目录
# ${PROJECT_NAME}_plugin 是您插件库的目标名称
add_custom_command(
        TARGET ${PROJECT_NAME}_plugin
        POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${BLUETOOTH_DLL_PATH}"
        "$<TARGET_FILE_DIR:${PROJECT_NAME}_plugin>"
        COMMENT "Copying bluetooth_detector.dll to plugin build directory"
)

# 移除原有的 _bundled_libraries 设置，因为它不负责文件复制
# set(${PROJECT_NAME}_bundled_libraries
#         "${CMAKE_CURRENT_SOURCE_DIR}/libs/bluetooth_detector.dll"
#         PARENT_SCOPE
# )
