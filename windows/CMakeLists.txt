# The Flutter tooling requires that developers have a version of Visual Studio
# installed that includes CMake 3.14 or later. You should not increase this
# version, as doing so will cause the plugin to fail to compile for some
# customers of the plugin.
cmake_minimum_required(VERSION 3.14)

# Project-level configuration.
set(PROJECT_NAME "niimbot_pc_driver_status")
project(${PROJECT_NAME} LANGUAGES CXX)

# 动态库存放目录
set(BUILD_BUNDLE_DIR "$<TARGET_FILE_DIR:${BINARY_NAME}>")

# 安装动态库 到执行目录
install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/libs/bluetooth_detector.dll" DESTINATION "${BUILD_BUNDLE_DIR}" COMPONENT Runtime)
