#ifndef FLUTTER_PLUGIN_NIIMBOT_PC_DRIVER_STATUS_PLUGIN_H_
#define FLUTTER_PLUGIN_NIIMBOT_PC_DRIVER_STATUS_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace niimbot_pc_driver_status {

class NiimbotPcDriverStatusPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  NiimbotPcDriverStatusPlugin();

  virtual ~NiimbotPcDriverStatusPlugin();

  // Disallow copy and assign.
  NiimbotPcDriverStatusPlugin(const NiimbotPcDriverStatusPlugin&) = delete;
  NiimbotPcDriverStatusPlugin& operator=(const NiimbotPcDriverStatusPlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace niimbot_pc_driver_status

#endif  // FLUTTER_PLUGIN_NIIMBOT_PC_DRIVER_STATUS_PLUGIN_H_
