#include "niimbot_pc_driver_status_plugin.h"

// 必须包含在其他 Windows 头文件之前
#include <windows.h>

// 示例方法
#include <VersionHelpers.h>

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>

#include <memory>
#include <sstream>

namespace niimbot_pc_driver_status {

    void NiimbotPcDriverStatusPlugin::RegisterWithRegistrar(
            flutter::PluginRegistrarWindows *registrar) {
        auto channel =
                std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
                        registrar->messenger(), "niimbot_pc_driver_status",
                                &flutter::StandardMethodCodec::GetInstance());

        auto plugin = std::make_unique<NiimbotPcDriverStatusPlugin>();

        channel->SetMethodCallHandler(
                [plugin_pointer = plugin.get()](const auto &call, auto result) {
                    plugin_pointer->HandleMethodCall(call, std::move(result));
                });

        registrar->AddPlugin(std::move(plugin));
    }

    NiimbotPcDriverStatusPlugin::NiimbotPcDriverStatusPlugin() {}

    NiimbotPcDriverStatusPlugin::~NiimbotPcDriverStatusPlugin() {}

    void NiimbotPcDriverStatusPlugin::HandleMethodCall(
            const flutter::MethodCall<flutter::EncodableValue> &method_call,
            std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
        if (method_call.method_name().compare("getPlatformVersion") == 0) {
            std::ostringstream version_stream;
            version_stream << "Windows ";
            if (IsWindows10OrGreater()) {
                version_stream << "10+";
            } else if (IsWindows8OrGreater()) {
                version_stream << "8";
            } else if (IsWindows7OrGreater()) {
                version_stream << "7";
            }
            result->Success(flutter::EncodableValue(version_stream.str()));
        } else {
            result->NotImplemented();
        }
    }

}  // namespace niimbot_pc_driver_status

// ==== 插件注册入口（供 Flutter 框架调用） ====
extern "C" __declspec(dllexport) void NiimbotPcDriverStatusPluginRegisterWithRegistrar(
        FlutterDesktopPluginRegistrarRef registrar) {
    niimbot_pc_driver_status::NiimbotPcDriverStatusPlugin::RegisterWithRegistrar(
            flutter::PluginRegistrarManager::GetInstance()
                    ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
