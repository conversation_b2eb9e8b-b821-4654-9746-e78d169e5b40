#ifndef NIIMBOT_PC_DRIVER_STATUS_PLUGIN_C_API_H_
#define NIIMBOT_PC_DRIVER_STATUS_PLUGIN_C_API_H_

// C API for bluetooth_detector.dll
// This header defines the interface for the Bluetooth detection functionality

#ifdef _WIN32
    #ifdef BT_EXPORTS
        #define BT_API __declspec(dllexport)
    #else
        #define BT_API __declspec(dllimport)
    #endif
    #define BT_CALL __stdcall
#else
    #define BT_API
    #define BT_CALL
#endif

#ifdef __cplusplus
extern "C" {
#endif

// C接口函数声明（供DLL导出）
BT_API bool BT_CALL IsBluetoothDriverWorking();
BT_API bool BT_CALL IsBluetoothPresent();
BT_API bool BT_CALL IsBluetoothEnabled();
BT_API int BT_CALL GetLastErrorCode();
BT_API const char* BT_CALL GetLastErrorMessage();
BT_API void BT_CALL RefreshBluetoothStatus();
BT_API void BT_CALL ConfigureLogger(int logLevel, int logTarget, const char* logPath);
BT_API const char* BT_CALL GetLibraryVersion();
BT_API const char* BT_CALL GetPlatformInfo();

#ifdef __cplusplus
}
#endif

#endif  // NIIMBOT_PC_DRIVER_STATUS_PLUGIN_C_API_H_
