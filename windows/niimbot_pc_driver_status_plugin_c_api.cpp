#include "include/niimbot_pc_driver_status/niimbot_pc_driver_status_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "niimbot_pc_driver_status_plugin.h"

void NiimbotPcDriverStatusPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  niimbot_pc_driver_status::NiimbotPcDriverStatusPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
