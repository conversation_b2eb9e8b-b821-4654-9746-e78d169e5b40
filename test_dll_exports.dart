import 'dart:ffi';
import 'dart:io';
import 'package:path/path.dart' as path;

void main() {
  try {
    final String libraryPath;
    final executableDir = path.dirname(Platform.resolvedExecutable);
    libraryPath = path.join(executableDir, 'bluetooth_detector.dll');
    
    print('Trying to load DLL from: $libraryPath');
    print('DLL exists: ${File(libraryPath).existsSync()}');
    
    if (!File(libraryPath).existsSync()) {
      print('DLL not found!');
      return;
    }
    
    final dylib = DynamicLibrary.open(libraryPath);
    print('DLL loaded successfully!');
    
    // Try to lookup each function and see what happens
    final functions = [
      'IsBluetoothDriverWorking',
      'IsBluetoothPresent', 
      'IsBluetoothEnabled',
      'GetLastErrorCode',
      'GetLastErrorMessage',
      'RefreshBluetoothStatus',
      'ConfigureLogger',
      'GetLibraryVersion',
      'GetPlatformInfo',
    ];
    
    for (final funcName in functions) {
      try {
        final func = dylib.lookup(funcName);
        print('✓ Found function: $funcName at address: ${func.address.toRadixString(16)}');
      } catch (e) {
        print('✗ Function not found: $funcName - $e');
      }
    }
    
  } catch (e) {
    print('Error: $e');
  }
}
