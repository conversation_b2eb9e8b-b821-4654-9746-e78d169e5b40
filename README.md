# Niimbot PC Driver Status

一个用于检测 Windows 系统驱动状态的 Flutter 插件。目前支持蓝牙驱动检测，可扩展支持更多驱动类型。

## 功能特性

- 🔍 **蓝牙驱动检测**：通过原生 DLL 检测 Windows 系统蓝牙驱动是否正常工作
- 🚀 **高性能**：使用 Dart FFI 直接调用原生代码，性能优异
- 🔧 **易于扩展**：架构设计支持添加更多驱动检测功能
- 📦 **自动打包**：CMake 配置自动处理 DLL 依赖

## 架构说明

### 调用链路

```
Flutter 应用层
    ↓
BluetoothDriverDetector (Dart FFI 层)
    ↓
bluetooth_detector.dll (原生 DLL)
    ↓
Windows Driver API
```

### 详细说明

1. **Flutter 应用层** (`example/lib/main.dart`)
   - 提供用户界面
   - 调用插件接口获取驱动状态
   - 展示检测结果

2. **Dart FFI 层** (`lib/bluetooth_driver_detector.dart`)
   - 定义 C++ 函数签名
   - 动态加载 DLL
   - 提供 Dart 接口供应用调用

3. **原生 DLL** (`windows/libs/bluetooth_detector.dll`)
   - 实现驱动检测核心逻辑
   - 调用 Windows 系统 API
   - 返回驱动状态

4. **CMake 配置** (`windows/CMakeLists.txt`)
   - 自动处理 DLL 打包
   - 确保 DLL 复制到正确位置

## 安装使用

### 1. 添加依赖

在 `pubspec.yaml` 中添加：

```yaml
dependencies:
  niimbot_pc_driver_status:
    path: ../niimbot_pc_driver_status  # 或使用 git/pub 地址
```

### 2. 使用示例

```dart
import 'package:niimbot_pc_driver_status/bluetooth_driver_detector.dart';

// 检测蓝牙驱动状态
bool isBluetoothWorking = BluetoothDriverDetector.isBluetoothDriverWorking();

if (isBluetoothWorking) {
  print("蓝牙驱动正常");
} else {
  print("蓝牙驱动异常或未安装");
}
```

### 3. 完整示例

查看 `example/lib/main.dart` 获取完整的使用示例，包括：
- UI 界面展示
- 状态刷新
- 错误处理

## API 文档

### BluetoothDriverDetector

#### isBluetoothDriverWorking()

检测系统蓝牙驱动是否正常工作。

**返回值**：
- `true`：蓝牙驱动正常
- `false`：蓝牙驱动异常、未安装或检测失败

**示例**：
```dart
bool status = BluetoothDriverDetector.isBluetoothDriverWorking();
```

## 技术实现

### FFI 实现细节

1. **函数签名定义**：
```dart
typedef IsBluetoothDriverWorkingC = Bool Function();
typedef IsBluetoothDriverWorkingDart = bool Function();
```

2. **DLL 加载**：
```dart
DynamicLibrary.open('bluetooth_detector.dll');
```

3. **函数查找与调用**：
```dart
final isWorking = _bluetoothLib!.lookupFunction<
    IsBluetoothDriverWorkingC,
    IsBluetoothDriverWorkingDart>('IsBluetoothDriverWorking');
```

### CMake 配置

CMakeLists.txt 关键配置：

```cmake
# 设置 bundled_libraries
set(niimbot_pc_driver_status_bundled_libraries
  "${CMAKE_CURRENT_SOURCE_DIR}/libs/bluetooth_detector.dll"
  PARENT_SCOPE
)

# 安装 DLL 到输出目录
install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/libs/bluetooth_detector.dll" 
        DESTINATION "${BUILD_BUNDLE_DIR}"
        CONFIGURATIONS Debug;Profile;Release
        COMPONENT Runtime)
```

## 扩展开发

### 添加新的驱动检测

1. **准备原生 DLL**
   - 创建新的 DLL 实现驱动检测逻辑
   - 放置到 `windows/libs/` 目录

2. **创建 Dart FFI 接口**
   ```dart
   class NewDriverDetector {
     static bool isDriverWorking() {
       // FFI 实现
     }
   }
   ```

3. **更新 CMakeLists.txt**
   添加新 DLL 到 bundled_libraries 和 install 配置

4. **测试验证**
   在 example 项目中测试新功能

## 平台支持

- ✅ Windows (7/8/10/11)
- ❌ macOS (不支持)
- ❌ Linux (不支持)
- ❌ Android (不支持)
- ❌ iOS (不支持)

## 注意事项

1. **仅支持 Windows 平台**：插件专为 Windows 设计，其他平台会抛出 `UnsupportedError`
2. **需要管理员权限**：某些驱动检测可能需要管理员权限
3. **DLL 依赖**：确保 bluetooth_detector.dll 正确打包

## 故障排除

### DLL 加载失败

如果遇到 "Failed to load bluetooth_detector.dll" 错误：

1. 确认 DLL 文件存在于 `windows/libs/` 目录
2. 检查 CMakeLists.txt 配置是否正确
3. 重新构建项目：`flutter clean && flutter build windows`

### 驱动检测返回 false

可能的原因：
1. 蓝牙驱动未安装
2. 蓝牙设备被禁用
3. 驱动程序有问题

解决方法：
1. 打开设备管理器检查蓝牙设备状态
2. 更新或重新安装蓝牙驱动
3. 确保蓝牙服务已启动

## 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发环境要求

- Flutter SDK
- Visual Studio 2019 或更高版本
- CMake 3.14+
- Windows SDK

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。